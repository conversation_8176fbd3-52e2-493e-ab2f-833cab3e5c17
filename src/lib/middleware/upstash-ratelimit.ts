import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

const UPSTASH_REDIS_REST_URL = process.env.UPSTASH_REDIS_REST_URL;
const UPSTASH_REDIS_REST_TOKEN = process.env.UPSTASH_REDIS_REST_TOKEN;

if (!UPSTASH_REDIS_REST_URL || !UPSTASH_REDIS_REST_TOKEN) {
	throw new Error("Upstash Redis environment variables are not set");
}

// Rate limit configuration
// Regular API routes: 60 requests per minute
// Widget API routes: 120 requests per minute (more lenient for embedded widgets)
const WINDOW_SECONDS = 60;
const REGULAR_REQUEST_LIMIT = 60;
const WIDGET_REQUEST_LIMIT = 120;

export async function upstashRateLimitMiddleware(
	request: NextRequest,
): Promise<NextResponse | null> {
	// Use IP address as identifier
	const ip =
		request.headers.get("x-forwarded-for") ||
		request.headers.get("x-real-ip") ||
		"anonymous";

	// Check if this is a widget API route
	const isWidgetRoute =
		request.nextUrl.pathname.startsWith("/api/widget") ||
		request.nextUrl.pathname.startsWith("/api/chat");

	// Use different rate limits for widget routes
	const requestLimit = isWidgetRoute
		? WIDGET_REQUEST_LIMIT
		: REGULAR_REQUEST_LIMIT;

	// Add route type to the identifier to separate widget and regular API rate limits
	const routeType = isWidgetRoute ? "widget" : "api";
	const identifier = `${routeType}:ip:${ip}`;
	const key = `ratelimit:${identifier}`;

	// Upstash INCR with EXPIRE in a pipeline
	const incrRes = await fetch(`${UPSTASH_REDIS_REST_URL}/pipeline`, {
		method: "POST",
		headers: {
			Authorization: `Bearer ${UPSTASH_REDIS_REST_TOKEN}`,
			"Content-Type": "application/json",
		},
		body: JSON.stringify([
			["INCR", key],
			["EXPIRE", key, WINDOW_SECONDS],
		]),
		cache: "no-store",
	});

	if (!incrRes.ok) {
		// On error, allow the request but log
		console.error("Upstash rate limit error", await incrRes.text());
		return null;
	}

	const data = await incrRes.json();
	const count = Array.isArray(data) && data.length > 0 ? data[0].result : 0;

	// Common rate limit headers
	const rateHeaders = {
		"X-RateLimit-Limit": requestLimit.toString(),
		"X-RateLimit-Remaining": Math.max(0, requestLimit - count).toString(),
		"X-RateLimit-Reset": WINDOW_SECONDS.toString(),
	};

	if (count > requestLimit) {
		// Rate limit exceeded
		return new NextResponse("Too Many Requests", {
			status: 429,
			headers: {
				"Retry-After": WINDOW_SECONDS.toString(),
				...rateHeaders,
			},
		});
	}

	// Not rate limited, but return headers for tracking
	return new NextResponse(null, {
		status: 200,
		headers: rateHeaders,
	});
}
