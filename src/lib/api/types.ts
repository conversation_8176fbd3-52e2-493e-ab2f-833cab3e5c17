export interface ApiResponse<T> {
	data: T;
	error?: never;
}

export interface ApiError {
	error: {
		message: string;
		code: string;
	};
	data?: never;
}

export interface RateLimitInfo {
	limit: number;
	remaining: number;
	reset: number;
}

export interface Website {
	id: string;
	name: string;
	url: string;
	createdAt: string;
	updatedAt: string;
	lastCrawledAt?: string;
	crawlFrequency?: string;
	status?: string;
	conversationCount?: number;
}

export interface CreateWebsiteInput {
	name: string;
	url: string;
}

export interface UpdateWebsiteInput {
	name?: string;
	url?: string;
}

export type ApiResult<T> = ApiResponse<T> | ApiError;
