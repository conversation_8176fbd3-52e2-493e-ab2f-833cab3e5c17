import type { RateLimitInfo } from "./types";

class ApiRequestError extends Error {
	constructor(
		message: string,
		public code: string,
		public status: number,
		public rateLimitInfo?: RateLimitInfo,
	) {
		super(message);
		this.name = "ApiRequestError";
	}
}

export async function fetchApi<T>(
	endpoint: string,
	options: RequestInit = {},
): Promise<T> {
	const defaultHeaders: HeadersInit = {
		"Content-Type": "application/json",
	};

	const response = await fetch(`/api${endpoint}`, {
		...options,
		headers: {
			...defaultHeaders,
			...options.headers,
		},
	});

	// Extract rate limit information if present
	const rateLimitInfo: Partial<RateLimitInfo> = {
		limit: Number(response.headers.get("X-RateLimit-Limit")),
		remaining: Number(response.headers.get("X-RateLimit-Remaining")),
		reset: Number(response.headers.get("X-RateLimit-Reset")),
	};

	// Handle rate limit exceeded
	if (response.status === 429) {
		throw new ApiRequestError(
			"Rate limit exceeded",
			"RATE_LIMIT_EXCEEDED",
			429,
			rateLimitInfo as RateLimitInfo,
		);
	}

	const responseData = await response.json();

	// Handle both API response formats:
	// 1. { data: T, error?: never } - Standard API result
	// 2. { success: boolean, data: T, error?: string } - Dashboard API format

	if (responseData.success === false) {
		throw new ApiRequestError(
			responseData.error || "Unknown error",
			"API_ERROR",
			response.status,
			rateLimitInfo as RateLimitInfo,
		);
	}

	// If the response has a success property and data property, return the data
	if (responseData.success === true && responseData.data) {
		return responseData.data as T;
	}

	// If the response has an error property, throw an error
	if ("error" in responseData && responseData.error) {
		throw new ApiRequestError(
			typeof responseData.error === "string"
				? responseData.error
				: responseData.error.message || "Unknown error",
			typeof responseData.error === "string"
				? "API_ERROR"
				: responseData.error.code || "API_ERROR",
			response.status,
			rateLimitInfo as RateLimitInfo,
		);
	}

	// If the response doesn't have a data property, return the whole response
	return (responseData.data || responseData) as T;
}

export const api = {
	get: <T>(endpoint: string, options?: RequestInit) =>
		fetchApi<T>(endpoint, { ...options, method: "GET" }),

	post: <T>(endpoint: string, data: unknown, options?: RequestInit) =>
		fetchApi<T>(endpoint, {
			...options,
			method: "POST",
			body: JSON.stringify(data),
		}),

	put: <T>(endpoint: string, data: unknown, options?: RequestInit) =>
		fetchApi<T>(endpoint, {
			...options,
			method: "PUT",
			body: JSON.stringify(data),
		}),

	patch: <T>(endpoint: string, data: unknown, options?: RequestInit) =>
		fetchApi<T>(endpoint, {
			...options,
			method: "PATCH",
			body: JSON.stringify(data),
		}),

	delete: <T>(endpoint: string, options?: RequestInit) =>
		fetchApi<T>(endpoint, { ...options, method: "DELETE" }),
};
