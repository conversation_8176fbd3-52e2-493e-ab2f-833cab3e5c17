import { createEnv } from "@t3-oss/env-nextjs";
import { z } from "zod";

export const env = createEnv({
	server: {
		DATABASE_URL: z.string().url(),
		CLERK_WEBHOOK_SECRET: z.string(),
		CLERK_SECRET_KEY: z.string(),
		REDIS_URL: z.string(),
		REDIS_TOKEN: z.string().optional(),
		UPSTASH_REDIS_REST_URL: z.string().url(),
		UPSTASH_REDIS_REST_TOKEN: z.string(),
		NODE_ENV: z.enum(["development", "test", "production"]),
		BROWSERLESS_URL: z.string().url().optional(),
		BROWSERLESS_API_KEY: z.string().optional(),
		USE_BROWSERLESS: z.enum(["true", "false"]).optional().default("false"),
		CSRF_SECRET: z.string().optional().default("bubl-csrf-secret"),
		INNGEST_EVENT_KEY: z.string().optional(),
		INNGEST_SIGNING_KEY: z.string().optional(),
	},

	client: {
		NEXT_PUBLIC_API_URL: z.string().url().optional(),
		NEXT_PUBLIC_APP_URL: z.string().url().optional(),
	},
	// If you're using Next.js < 13.4.4, you'll need to specify the runtimeEnv manually
	runtimeEnv: {
		DATABASE_URL: process.env.DATABASE_URL,
		CLERK_WEBHOOK_SECRET: process.env.CLERK_WEBHOOK_SECRET,
		REDIS_URL: process.env.REDIS_URL ?? "redis://localhost:6379",
		REDIS_TOKEN: process.env.REDIS_TOKEN,
		UPSTASH_REDIS_REST_URL: process.env.UPSTASH_REDIS_REST_URL,
		UPSTASH_REDIS_REST_TOKEN: process.env.UPSTASH_REDIS_REST_TOKEN,
		NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
		CLERK_SECRET_KEY: process.env.CLERK_SECRET_KEY,
		NODE_ENV: process.env.NODE_ENV,
		NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
		BROWSERLESS_URL: process.env.BROWSERLESS_URL,
		BROWSERLESS_API_KEY: process.env.BROWSERLESS_API_KEY,
		USE_BROWSERLESS: process.env.USE_BROWSERLESS,
		CSRF_SECRET: process.env.CSRF_SECRET,
		INNGEST_EVENT_KEY: process.env.INNGEST_EVENT_KEY,
		INNGEST_SIGNING_KEY: process.env.INNGEST_SIGNING_KEY,
	},
	// For Next.js >= 13.4.4, you only need to destructure client variables:
	// experimental__runtimeEnv: {
	//   NEXT_PUBLIC_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_PUBLISHABLE_KEY,
	// }
});
