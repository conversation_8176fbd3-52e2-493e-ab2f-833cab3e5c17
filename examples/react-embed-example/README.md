# Bubl React Embed Example

This is an example of how to use the Bubl chat widget in a React application.

## Getting Started

1. Install dependencies:

```bash
npm install
# or
yarn
# or
pnpm install
```

2. Update the `websiteId` in `App.jsx` with your actual website ID from the Bubl dashboard.

3. Start the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser to see the result.

## How It Works

The example uses the `@bubl/react` package to embed the Bubl chat widget in a React application. The widget is configured using props:

```jsx
<BublChat 
  websiteId="your-website-id"
  primaryColor="#4F46E5"
  secondaryColor="#FFFFFF"
  position="bottom-right"
  welcomeMessage="Hi there! How can I help you today?"
  headerText="Chat Assistant"
  initiallyOpen={false}
  onReady={() => console.log("Bubl widget is ready")}
/>
```

## Customization

You can customize the widget by changing the props:

- `websiteId`: Your website ID from the Bubl dashboard
- `primaryColor`: Primary color for the chat widget (hex code)
- `secondaryColor`: Secondary color for the chat widget (hex code)
- `position`: Position of the chat widget (`bottom-right` or `bottom-left`)
- `welcomeMessage`: Welcome message displayed when the chat is opened
- `headerText`: Text displayed in the header of the chat widget
- `initiallyOpen`: Whether the chat widget should be initially open
- `onReady`: Callback function called when the widget is ready

## API Control

You can control the widget programmatically using the global `Bubl.api` object:

```jsx
// Open the chat widget
window.Bubl?.api?.open();

// Close the chat widget
window.Bubl?.api?.close();

// Toggle the chat widget
window.Bubl?.api?.toggle();
```

## Learn More

For more information, check out the [@bubl/react documentation](../../packages/react/README.md).
