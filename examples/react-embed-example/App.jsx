import React from 'react';
import { BublChat } from '@bubl/react';

function App() {
  return (
    <div className="App">
      <header className="App-header">
        <h1>Bubl Chat Example</h1>
        <p>
          This is an example of how to use the Bubl chat widget in a React application.
        </p>
      </header>
      
      <main>
        <h2>Website Content</h2>
        <p>
          This is your website content. The chat widget will appear in the bottom right corner.
        </p>
        <p>
          You can customize the appearance and behavior of the widget using the props.
        </p>
      </main>
      
      {/* Bubl Chat Widget */}
      <BublChat 
        websiteId="your-website-id"
        primaryColor="#4F46E5"
        secondaryColor="#FFFFFF"
        position="bottom-right"
        welcomeMessage="Hi there! How can I help you today?"
        headerText="Chat Assistant"
        initiallyOpen={false}
        onReady={() => console.log("Bubl widget is ready")}
      />
    </div>
  );
}

export default App;
